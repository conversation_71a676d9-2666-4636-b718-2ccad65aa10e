"""Add notification model

Revision ID: db2e33e19217
Revises: b8f46481d615
Create Date: 2025-01-18 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'db2e33e19217'
down_revision: Union[str, None] = 'b8f46481d615'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Check if enum types exist, if not create them
    connection = op.get_bind()
    
    # Check notification_type enum
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM pg_type 
            WHERE typname = 'notificationtype'
        )
    """)).scalar()
    
    if not result:
        notification_type = sa.Enum('info', 'success', 'warning', 'error', 'task_complete', 
                                  'new_message', 'system_update', 'content_recommendation', 
                                  'account_activity', name='notificationtype')
        notification_type.create(connection)
    
    # Check notification_priority enum
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM pg_type 
            WHERE typname = 'notificationpriority'
        )
    """)).scalar()
    
    if not result:
        notification_priority = sa.Enum('low', 'normal', 'high', 'urgent', name='notificationpriority')
        notification_priority.create(connection)
    
    # Check notification_status enum
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM pg_type 
            WHERE typname = 'notificationstatus'
        )
    """)).scalar()
    
    if not result:
        notification_status = sa.Enum('unread', 'read', 'deleted', name='notificationstatus')
        notification_status.create(connection)
    
    # Check if notifications table exists
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'notifications'
        )
    """)).scalar()
    
    if not result:
        # Create notifications table
        op.create_table('notifications',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('user_id', sa.Integer(), nullable=False),
            sa.Column('type', sa.Enum('info', 'success', 'warning', 'error', 'task_complete', 
                                    'new_message', 'system_update', 'content_recommendation', 
                                    'account_activity', name='notificationtype'), nullable=False),
            sa.Column('priority', sa.Enum('low', 'normal', 'high', 'urgent', name='notificationpriority'), nullable=True),
            sa.Column('title', sa.String(length=200), nullable=False),
            sa.Column('message', sa.Text(), nullable=False),
            sa.Column('status', sa.Enum('unread', 'read', 'deleted', name='notificationstatus'), nullable=True),
            sa.Column('data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
            sa.Column('action_url', sa.String(length=500), nullable=True),
            sa.Column('expires_at', sa.DateTime(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('read_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        
        # Create indexes
        op.create_index(op.f('ix_notifications_type'), 'notifications', ['type'], unique=False)
        op.create_index(op.f('ix_notifications_priority'), 'notifications', ['priority'], unique=False)
        op.create_index(op.f('ix_notifications_status'), 'notifications', ['status'], unique=False)
        op.create_index(op.f('ix_notifications_created_at'), 'notifications', ['created_at'], unique=False)


def downgrade() -> None:
    # Drop indexes
    op.drop_index(op.f('ix_notifications_created_at'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_status'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_priority'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_type'), table_name='notifications')
    
    # Drop table
    op.drop_table('notifications')
    
    # Drop enums
    sa.Enum(name='notificationstatus').drop(op.get_bind())
    sa.Enum(name='notificationpriority').drop(op.get_bind())
    sa.Enum(name='notificationtype').drop(op.get_bind())