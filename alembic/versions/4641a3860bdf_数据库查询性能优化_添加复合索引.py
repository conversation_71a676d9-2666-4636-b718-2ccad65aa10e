"""数据库查询性能优化_添加复合索引

Revision ID: 4641a3860bdf
Revises: b8f46481d615
Create Date: 2025-09-01 07:07:29.833181

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4641a3860bdf'
down_revision: Union[str, None] = 'b8f46481d615'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """添加性能优化索引 - 针对文章和视频查询优化"""
    
    print("开始创建数据库性能优化索引...")
    
    # 1. 文章表核心查询优化索引
    print("创建文章表索引...")
    
    # 分类页面查询优化：category_id + 状态字段 + 排序字段
    op.create_index(
        'ix_articles_category_published_approved_deleted_updated_at',
        'articles',
        ['category_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at'],
        postgresql_where=sa.text('is_published = true AND is_approved = true AND is_deleted = false')
    )
    
    # 作者文章列表查询优化
    op.create_index(
        'ix_articles_author_published_approved_deleted_updated_at',
        'articles',
        ['author_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at']
    )
    
    # 状态筛选复合索引（支持多状态查询）
    op.create_index(
        'ix_articles_status_updated_at',
        'articles',
        ['is_published', 'is_approved', 'is_deleted', 'updated_at'],
        postgresql_where=sa.text('is_deleted = false')
    )
    
    # 热门文章查询优化（访问量排序）
    op.create_index(
        'ix_articles_visit_count_created_at',
        'articles',
        ['visit_count', 'created_at'],
        postgresql_where=sa.text('is_published = true AND is_approved = true AND is_deleted = false')
    )
    
    # 2. 全文搜索索引（PostgreSQL GIN索引）
    print("创建全文搜索索引...")
    op.execute("""
        CREATE INDEX ix_articles_title_content_gin 
        ON articles USING gin(to_tsvector('chinese', coalesce(title, '') || ' ' || coalesce(content, '')))
        WHERE is_published = true AND is_approved = true AND is_deleted = false
    """)
    
    # 3. 点赞表优化索引
    print("创建点赞表索引...")
    
    # 用户点赞列表查询优化
    op.create_index(
        'ix_likes_user_content_active_created_at',
        'likes',
        ['user_id', 'content_type', 'is_active', 'created_at'],
        postgresql_where=sa.text('is_active = true')
    )
    
    # 内容点赞统计优化
    op.create_index(
        'ix_likes_content_active_count',
        'likes',
        ['content_id', 'content_type', 'is_active'],
        postgresql_where=sa.text('is_active = true')
    )
    
    # 4. 收藏表优化索引
    print("创建收藏表索引...")
    
    # 用户收藏列表查询优化
    op.create_index(
        'ix_favorites_user_content_active_created_at',
        'favorites',
        ['user_id', 'content_type', 'is_active', 'created_at'],
        postgresql_where=sa.text('is_active = true')
    )
    
    # 内容收藏统计优化
    op.create_index(
        'ix_favorites_content_active_count',
        'favorites',
        ['content_id', 'content_type', 'is_active'],
        postgresql_where=sa.text('is_active = true')
    )
    
    # 5. 历史记录表优化索引
    print("创建历史记录索引...")
    op.create_index(
        'ix_history_user_content_updated_at',
        'history',
        ['user_id', 'content_type', 'updated_at']
    )
    
    # 6. 视频表类似优化
    print("创建视频表索引...")
    
    # 视频文件夹查询优化
    op.create_index(
        'ix_videos_folder_published_approved_deleted_updated_at',
        'videos',
        ['folder_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at'],
        postgresql_where=sa.text('is_published = true AND is_approved = true AND is_deleted = false')
    )
    
    # 视频作者查询优化
    op.create_index(
        'ix_videos_author_published_approved_deleted_updated_at',
        'videos',
        ['author_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at']
    )
    
    # 7. 评论表优化索引
    print("创建评论表索引...")
    op.create_index(
        'ix_comments_content_created_at',
        'comments',
        ['content_id', 'content_type', 'created_at'],
        postgresql_where=sa.text('is_deleted = false')
    )
    
    # 8. 用户设备表优化索引（认证相关）
    print("创建用户设备索引...")
    op.create_index(
        'ix_user_devices_user_trusted_updated_at',
        'user_devices',
        ['user_id', 'is_trusted', 'updated_at']
    )
    
    print("性能优化索引创建完成！")


def downgrade() -> None:
    """删除性能优化索引"""
    
    print("开始删除性能优化索引...")
    
    # 按相反顺序删除索引
    op.drop_index('ix_user_devices_user_trusted_updated_at', 'user_devices')
    op.drop_index('ix_comments_content_created_at', 'comments')
    op.drop_index('ix_videos_author_published_approved_deleted_updated_at', 'videos')
    op.drop_index('ix_videos_folder_published_approved_deleted_updated_at', 'videos')
    op.drop_index('ix_history_user_content_updated_at', 'history')
    op.drop_index('ix_favorites_content_active_count', 'favorites')
    op.drop_index('ix_favorites_user_content_active_created_at', 'favorites')
    op.drop_index('ix_likes_content_active_count', 'likes')
    op.drop_index('ix_likes_user_content_active_created_at', 'likes')
    op.drop_index('ix_articles_title_content_gin', 'articles')
    op.drop_index('ix_articles_visit_count_created_at', 'articles')
    op.drop_index('ix_articles_status_updated_at', 'articles')
    op.drop_index('ix_articles_author_published_approved_deleted_updated_at', 'articles')
    op.drop_index('ix_articles_category_published_approved_deleted_updated_at', 'articles')
    
    print("性能优化索引删除完成！")
