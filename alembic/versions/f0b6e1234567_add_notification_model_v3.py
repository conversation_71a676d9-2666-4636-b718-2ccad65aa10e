"""Add notification model v3

Revision ID: f0b6e1234567
Revises: 9250b104347a
Create Date: 2025-01-18 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f0b6e1234567'
down_revision: Union[str, None] = '9250b104347a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create notifications table using raw SQL to avoid enum issues
    op.execute("""
        CREATE TABLE IF NOT EXISTS notifications (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            type VARCHAR(50) NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error', 'task_complete', 'new_message', 'system_update', 'content_recommendation', 'account_activity')),
            priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
            title VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            status VARCHAR(20) DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'deleted')),
            data JSONB,
            action_url VARCHAR(500),
            expires_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT NOW(),
            read_at TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS ix_notifications_type ON notifications(type);
        CREATE INDEX IF NOT EXISTS ix_notifications_priority ON notifications(priority);
        CREATE INDEX IF NOT EXISTS ix_notifications_status ON notifications(status);
        CREATE INDEX IF NOT EXISTS ix_notifications_created_at ON notifications(created_at);
        CREATE INDEX IF NOT EXISTS ix_notifications_user_id ON notifications(user_id);
        CREATE INDEX IF NOT EXISTS ix_notifications_user_status ON notifications(user_id, status);
    """)


def downgrade() -> None:
    op.execute("""
        DROP TABLE IF EXISTS notifications;
    """)