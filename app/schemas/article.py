from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, model_serializer, model_validator

from app.config import get_settings
from app.schemas.category import CategoryBase
from app.schemas.review import ReviewBase
from app.schemas.tag import TagBase
from app.schemas.user import UserAggregated

settings = get_settings()
CDN_DOMAIN = settings.OSS_CDN_DOMAIN


class ArticleStatus(str, Enum):
    """文章状态枚举"""

    ALL = "all"
    DRAFT = "draft"
    PUBLISHED_APPROVED = "published_approved"
    PUBLISHED_PENDING = "published_pending"
    PUBLISHED_REJECTED = "published_rejected"


class MultipleData(BaseModel):
    """批量获取文章详情的响应模型"""

    articleIds: list[int]


class ContentStats(BaseModel):
    """内容统计信息"""

    like_count: int = Field(0, description="点赞数")
    favorite_count: int = Field(0, description="收藏数")
    visit_count: int = Field(0, description="访问次数")
    comment_count: int = Field(0, description="评论数")
    share_count: int = Field(0, description="分享数")

    # 用户相关统计
    is_liked_by_user: bool = Field(False, description="当前用户是否已点赞")
    is_favorited_by_user: bool = Field(False, description="当前用户是否已收藏")
    is_followed_author: bool = Field(False, description="当前用户是否已关注作者")


class ContentMeta(BaseModel):
    """内容元数据"""

    content_type: str = Field("article", description="内容类型: article, video")
    content_id: int = Field(..., description="内容ID")
    slug: str | None = Field(None, description="内容别名")
    seo_title: str | None = Field(None, description="SEO标题")
    seo_description: str | None = Field(None, description="SEO描述")
    keywords: list[str] | None = Field(None, description="关键词")


class ArticleBase(BaseModel):
    """文章基础模型"""

    id: int = Field(..., description="文章ID")
    author_id: int = Field(..., description="作者ID")
    category_id: int | None = Field(None, description="分类ID")
    title: str = Field(..., description="标题")
    description: str | None = Field(None, description="描述")
    cover_url: str | None = Field(None, description="封面图URL")
    is_published: bool = Field(False, description="是否发布（草稿状态为False）")
    is_approved: bool = Field(False, description="是否通过审核")
    visit_count: int | None = Field(0, description="访问次数")
    created_at: datetime | None = Field(None, description="创建时间")
    updated_at: datetime | None = Field(None, description="更新时间")
    cache_version: int | None = Field(1, description="缓存版本号")
    tags: list[TagBase] | None = Field(None, description="标签列表")
    category: CategoryBase | None = Field(None, description="分类")

    model_config = ConfigDict(from_attributes=True)


class ArticleCreate(BaseModel):
    """创建文章的请求模型"""

    author_id: int
    title: str | None = None  # 可选标题，如果不提供则使用默认值
    content: str | None = None  # 可选内容，如果不提供则使用默认值


class ArticleUpdate(BaseModel):
    """更新文章的请求模型"""

    title: str | None = None
    content: str | None = None
    description: str | None = None
    cover_url: str | None = None
    is_published: bool | None = None
    tags: list[str] | None = None
    category_id: int | None = None


class ArticleInDBBase(ArticleBase):
    """数据库中文章的基础模型"""

    id: int
    author_id: int
    category_id: int | None = None
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class ArticleOut(ArticleInDBBase):
    """统一的文章响应模型，用于API输出"""

    author: UserAggregated
    created_at: datetime | None = Field(None, description="创建时间")
    updated_at: datetime | None = Field(None, description="更新时间")
    category: CategoryBase | None = None
    content: str | None = None
    stats: ContentStats | None = None
    meta: ContentMeta | None = None
    review: ReviewBase | None = None

    @model_validator(mode="before")
    def populate_meta(self, values: Any) -> Any:
        """
        在验证之前，根据文章数据动态填充meta字段
        """
        # 将ORM对象或Pydantic模型转成dict
        if not isinstance(values, dict):
            if hasattr(values, "model_dump"):
                values = values.model_dump()
            elif hasattr(values, "__dict__"):
                values = dict(values.__dict__)
            else:
                values = {}

        tags = values.get("tags", [])
        keywords = [tag.name for tag in tags if hasattr(tag, "name") and isinstance(tag.name, str)]
        content_id = values.get("id")
        slug = values.get("slug")

        values["meta"] = ContentMeta(
            content_type="article",
            content_id=content_id,
            slug=slug,
            keywords=keywords,
        )

        return values

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        # 对封面URL添加CDN域名前缀
        if "cover_url" in data and data["cover_url"]:
            url = data["cover_url"]
            if url and isinstance(url, str):
                # 如果是相对路径（以/开头），添加CDN域名
                if url.startswith("/"):
                    data["cover_url"] = CDN_DOMAIN + url
                # 如果是steam相关路径但没有域名，也添加CDN域名
                elif url.startswith("steam/") and not url.startswith(("http://", "https://")):
                    data["cover_url"] = CDN_DOMAIN + "/" + url

        return data

    model_config = ConfigDict(from_attributes=True)
